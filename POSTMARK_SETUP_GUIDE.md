# Postmark Email Service Integration Guide

## Overview
This guide provides comprehensive instructions for setting up <PERSON>mark as the email service provider for the Laravel application, including configuration for order status notifications and all other application emails.

## Table of Contents
1. [Postmark Account Setup](#postmark-account-setup)
2. [Laravel Configuration](#laravel-configuration)
3. [Environment Variables](#environment-variables)
4. [Testing the Integration](#testing-the-integration)
5. [Production Deployment](#production-deployment)
6. [Troubleshooting](#troubleshooting)
7. [Monitoring and Logging](#monitoring-and-logging)

## Postmark Account Setup

### 1. Create Postmark Account
1. Visit [https://postmarkapp.com](https://postmarkapp.com)
2. Sign up for a new account or log in to existing account
3. Choose appropriate plan (starts with free tier for development)

### 2. Create a Server
1. In Postmark dashboard, click "Create Server"
2. Choose "Transactional" server type for application emails
3. Name your server (e.g., "Zoo-mall Production" or "Zoo-mall Development")
4. Complete server setup

### 3. Get Server API Token
1. Go to your server settings
2. Navigate to "API Tokens" tab
3. Copy the "Server API token"
4. Keep this token secure - it will be used in your `.env` file

### 4. Configure Sender Signature
1. Go to "Sender Signatures" in your server
2. Add your domain email address (e.g., `<EMAIL>`)
3. Verify the email address through the confirmation email
4. Optionally set up DKIM authentication for better deliverability

### 5. Set Up Domain Authentication (Recommended)
1. Go to "Sender Signatures" → "Domains"
2. Add your domain (e.g., `zoomall.pl`)
3. Configure DNS records as instructed by Postmark
4. Verify domain ownership

## Laravel Configuration

The Laravel application has been pre-configured with Postmark support. The following files have been modified:

### Modified Files:
- `config/mail.php` - Enhanced Postmark configuration
- `config/logging.php` - Added dedicated mail logging channel
- `config/app.php` - Registered MailServiceProvider
- `app/Providers/MailServiceProvider.php` - Custom mail service provider
- `app/Listeners/MailEventListener.php` - Mail event logging
- `app/Exceptions/Handler.php` - Mail error handling

## Environment Variables

### Required Configuration
Update your `.env` file with the following Postmark settings:

```env
# Mail Configuration - Postmark
MAIL_MAILER=postmark
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Your App Name"

# Postmark Configuration
POSTMARK_TOKEN=your-postmark-server-token-here
POSTMARK_MESSAGE_STREAM_ID=outbound
POSTMARK_TIMEOUT=30
```

### Configuration Options Explained:

- **MAIL_MAILER**: Set to `postmark` to use Postmark service
- **MAIL_FROM_ADDRESS**: Must be a verified sender signature in Postmark
- **MAIL_FROM_NAME**: Display name for outgoing emails
- **POSTMARK_TOKEN**: Server API token from Postmark dashboard
- **POSTMARK_MESSAGE_STREAM_ID**: Message stream (usually "outbound" for transactional emails)
- **POSTMARK_TIMEOUT**: Request timeout in seconds

### Development vs Production
For development, you can keep the SMTP configuration and switch to Postmark for production:

```env
# Development (local)
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025

# Production
MAIL_MAILER=postmark
POSTMARK_TOKEN=your-production-token
```

## Testing the Integration

### 1. Test Command
Use the built-in test command to verify Postmark integration:

```bash
php artisan postmark:test --email=<EMAIL>
```

This command will:
- Check mail configuration
- Send a simple test email
- Test order status notifications
- Verify password reset functionality

### 2. Manual Testing
Test order status notifications by updating an order status:

```php
// In tinker or a test script
$order = App\Models\Order::first();
$order->update(['status' => 'shipped']);
```

### 3. Run Test Suite
Execute the notification tests:

```bash
php artisan test tests/Feature/OrderStatusNotificationTest.php
```

## Production Deployment

### 1. Pre-deployment Checklist
- [ ] Postmark account created and verified
- [ ] Domain authentication configured
- [ ] Sender signatures verified
- [ ] API token obtained and secured
- [ ] Environment variables configured
- [ ] Test emails sent successfully

### 2. Environment Setup
1. Update production `.env` file with Postmark credentials
2. Ensure `MAIL_FROM_ADDRESS` uses verified domain
3. Set appropriate `MAIL_FROM_NAME`
4. Configure queue workers for background email processing

### 3. Queue Configuration
Ensure queue workers are running for email processing:

```bash
# Start queue worker
php artisan queue:work --queue=default

# Or use Supervisor for production
sudo supervisorctl start laravel-worker:*
```

### 4. DNS Configuration
If using domain authentication, configure these DNS records:

```
# DKIM Record (example)
********._domainkey.yourdomain.com TXT "k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC..."

# Return-Path Domain
pm-bounces.yourdomain.com CNAME pm.mtasv.net
```

## Troubleshooting

### Common Issues

#### 1. Authentication Errors (401)
```
Error: Unauthorized (401)
```
**Solution**: Check POSTMARK_TOKEN in `.env` file

#### 2. Sender Signature Not Verified
```
Error: You tried to send to a recipient that has been marked as inactive
```
**Solution**: Verify sender email address in Postmark dashboard

#### 3. Domain Not Verified
```
Error: The sender signature used in the 'From' field is not a confirmed Sender Signature
```
**Solution**: Add and verify sender signature or domain in Postmark

#### 4. Rate Limiting
```
Error: Too many requests
```
**Solution**: Implement exponential backoff or contact Postmark support

### Debug Steps
1. Check mail logs: `tail -f storage/logs/mail.log`
2. Verify configuration: `php artisan config:show mail`
3. Test connectivity: `php artisan postmark:test`
4. Check Postmark dashboard for delivery statistics

## Monitoring and Logging

### 1. Application Logs
Mail events are logged to `storage/logs/mail.log` with details:
- Email sending attempts
- Successful deliveries
- Failed deliveries with error details
- Postmark-specific error codes

### 2. Postmark Dashboard
Monitor email delivery in Postmark dashboard:
- Delivery statistics
- Bounce tracking
- Spam complaints
- Open/click tracking (if enabled)

### 3. Error Handling
The application includes comprehensive error handling:
- Transport exceptions are caught and logged
- Failed emails don't break application flow
- Detailed error information for debugging

### 4. Queue Monitoring
Monitor email queue processing:
```bash
# Check queue status
php artisan queue:monitor

# View failed jobs
php artisan queue:failed
```

## Email Types Supported

The Postmark integration handles all application emails:

1. **Order Status Notifications**
   - Order confirmation
   - Payment confirmation
   - Shipping notifications
   - Delivery confirmations
   - Cancellation notices

2. **User Authentication**
   - Email verification
   - Password reset
   - Account notifications

3. **Promotional Emails**
   - Marketing campaigns
   - Discount notifications
   - Newsletter

## Best Practices

1. **Sender Reputation**
   - Use consistent sender addresses
   - Maintain low bounce rates
   - Handle unsubscribes properly

2. **Email Content**
   - Use responsive email templates
   - Include plain text versions
   - Optimize for mobile devices

3. **Monitoring**
   - Set up alerts for high bounce rates
   - Monitor delivery statistics
   - Track email performance

4. **Security**
   - Keep API tokens secure
   - Use environment variables
   - Rotate tokens periodically

## Support and Resources

- **Postmark Documentation**: https://postmarkapp.com/developer
- **Laravel Mail Documentation**: https://laravel.com/docs/mail
- **Postmark Support**: https://postmarkapp.com/support
- **Status Page**: https://status.postmarkapp.com/

## Quick Setup Checklist

For quick deployment, follow this checklist:

- [ ] 1. Create Postmark account at https://postmarkapp.com
- [ ] 2. Create a new server in Postmark dashboard
- [ ] 3. Copy the Server API token
- [ ] 4. Add sender signature (verify email address)
- [ ] 5. Update `.env` file with Postmark credentials
- [ ] 6. Test integration: `php artisan postmark:test --email=<EMAIL>`
- [ ] 7. Deploy to production
- [ ] 8. Start queue workers
- [ ] 9. Monitor mail logs and Postmark dashboard

## Conclusion

With this setup, your Laravel application will use Postmark for reliable email delivery with comprehensive logging and error handling. The order status notification system will work seamlessly with Postmark's infrastructure, ensuring customers receive timely updates about their orders.
