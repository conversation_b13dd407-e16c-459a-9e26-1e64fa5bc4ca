<?php

namespace App\Services\App;

use App\Models\Product;
use App\Models\Category;
use App\Models\User;
use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ProductRecommendationService
{
    protected $cachePrefix;
    protected $cacheTtl;
    protected $config;

    public function __construct()
    {
        $this->config = config('recommendations');
        $this->cachePrefix = $this->config['cache']['prefix'];
        $this->cacheTtl = $this->config['cache']['ttl'];
    }

    /**
     * Get product recommendations for cart upsells
     *
     * @param Cart $cart
     * @param int $limit
     * @return Collection
     */
    public function getCartUpsells(Cart $cart, int $limit = null): Collection
    {
        $limit = $limit ?? $this->config['limits']['cart_upsells'];

        if (!$this->config['cache']['enabled']) {
            return $this->generateCartUpsells($cart, $limit);
        }

        $cacheKey = $this->getCacheKey('cart_upsells', $cart->id, $limit);

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($cart, $limit) {
            return $this->generateCartUpsells($cart, $limit);
        });
    }

    /**
     * Generate cart upsells without caching
     *
     * @param Cart $cart
     * @param int $limit
     * @return Collection
     */
    protected function generateCartUpsells(Cart $cart, int $limit): Collection
    {
        $recommendations = collect();

        // Get cart items with products
        $cartItems = $cart->items()->with(['product.category'])->get();
        $cartProductIds = $cartItems->pluck('product_id')->toArray();

        if ($cartItems->isEmpty()) {
            return $this->getFallbackRecommendations($limit);
        }

        // Strategy 1: Cross-sell related products (same category)
        if ($this->config['strategies']['cross_sell']['enabled']) {
            $crossSellProducts = $this->getCrossSellProducts($cartItems, $cartProductIds, $limit);
            $recommendations = $recommendations->merge($crossSellProducts);
        }

        // Strategy 2: User purchase history based recommendations
        if ($this->config['strategies']['user_history']['enabled'] && Auth::check()) {
            $historyProducts = $this->getUserHistoryRecommendations(Auth::user(), $cartProductIds, $limit);
            $recommendations = $recommendations->merge($historyProducts);
        }

        // Strategy 3: Popular products in same categories
        if ($this->config['strategies']['popular_products']['enabled']) {
            $popularProducts = $this->getPopularProductsInCategories($cartItems, $cartProductIds, $limit);
            $recommendations = $recommendations->merge($popularProducts);
        }

        // Strategy 4: Frequently bought together
        if ($this->config['strategies']['frequently_bought_together']['enabled']) {
            $frequentlyBoughtTogether = $this->getFrequentlyBoughtTogether($cartProductIds, $limit);
            $recommendations = $recommendations->merge($frequentlyBoughtTogether);
        }

        // Remove duplicates and limit results
        $recommendations = $recommendations->unique('id')->take($limit);

        // If we don't have enough recommendations, fill with fallback
        if ($recommendations->count() < $limit) {
            $fallback = $this->getFallbackRecommendations($limit - $recommendations->count(), $recommendations->pluck('id')->toArray());
            $recommendations = $recommendations->merge($fallback);
        }

        return $recommendations->take($limit);
    }

    /**
     * Get cross-sell products from same categories as cart items
     *
     * @param Collection $cartItems
     * @param array $excludeProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getCrossSellProducts(Collection $cartItems, array $excludeProductIds, int $limit): Collection
    {
        $categoryIds = $cartItems->pluck('product.category_id')->unique()->filter();
        
        if ($categoryIds->isEmpty()) {
            return collect();
        }
        
        return Product::inStock()
            ->whereIn('category_id', $categoryIds)
            ->whereNotIn('id', $excludeProductIds)
            ->with(['category', 'media'])
            ->orderBy('stock', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recommendations based on user's purchase history
     *
     * @param User $user
     * @param array $excludeProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getUserHistoryRecommendations(User $user, array $excludeProductIds, int $limit): Collection
    {
        // Get categories from user's previous orders
        $purchasedCategoryIds = OrderItem::whereHas('order', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->where('status', 'completed');
        })
        ->with('product.category')
        ->get()
        ->pluck('product.category_id')
        ->unique()
        ->filter();
        
        if ($purchasedCategoryIds->isEmpty()) {
            return collect();
        }
        
        return Product::inStock()
            ->whereIn('category_id', $purchasedCategoryIds)
            ->whereNotIn('id', $excludeProductIds)
            ->with(['category', 'media'])
            ->orderBy('stock', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get popular products in the same categories as cart items
     *
     * @param Collection $cartItems
     * @param array $excludeProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getPopularProductsInCategories(Collection $cartItems, array $excludeProductIds, int $limit): Collection
    {
        $categoryIds = $cartItems->pluck('product.category_id')->unique()->filter();
        
        if ($categoryIds->isEmpty()) {
            return collect();
        }
        
        // Get products ordered by how many times they've been ordered
        return Product::inStock()
            ->whereIn('products.category_id', $categoryIds)
            ->whereNotIn('products.id', $excludeProductIds)
            ->with(['category', 'media'])
            ->leftJoin('order_items', 'products.id', '=', 'order_items.product_id')
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->where(function ($query) {
                $query->whereNull('orders.status')
                      ->orWhere('orders.status', 'completed');
            })
            ->select('products.*', DB::raw('COUNT(order_items.id) as order_count'))
            ->groupBy('products.id')
            ->orderBy('order_count', 'desc')
            ->orderBy('products.created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get products frequently bought together with cart items
     *
     * @param array $cartProductIds
     * @param int $limit
     * @return Collection
     */
    protected function getFrequentlyBoughtTogether(array $cartProductIds, int $limit): Collection
    {
        if (empty($cartProductIds)) {
            return collect();
        }
        
        // Find orders that contain any of the cart products
        $relatedOrderIds = OrderItem::whereIn('product_id', $cartProductIds)
            ->whereHas('order', function ($query) {
                $query->where('status', 'completed');
            })
            ->pluck('order_id')
            ->unique();
        
        if ($relatedOrderIds->isEmpty()) {
            return collect();
        }
        
        // Find other products in those orders
        return Product::inStock()
            ->whereNotIn('id', $cartProductIds)
            ->whereHas('orderItems', function ($query) use ($relatedOrderIds) {
                $query->whereIn('order_id', $relatedOrderIds);
            })
            ->with(['category', 'media'])
            ->withCount(['orderItems' => function ($query) use ($relatedOrderIds) {
                $query->whereIn('order_id', $relatedOrderIds);
            }])
            ->orderBy('order_items_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get fallback recommendations when other strategies don't provide enough results
     *
     * @param int $limit
     * @param array $excludeProductIds
     * @return Collection
     */
    protected function getFallbackRecommendations(int $limit, array $excludeProductIds = []): Collection
    {
        return Product::inStock()
            ->whereNotIn('id', $excludeProductIds)
            ->with(['category', 'media'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Generate cache key for recommendations
     *
     * @param string $type
     * @param mixed $identifier
     * @param int $limit
     * @return string
     */
    protected function getCacheKey(string $type, $identifier, int $limit): string
    {
        $userId = Auth::id() ?? 'guest';
        return "{$this->cachePrefix}:{$type}:{$identifier}:{$userId}:{$limit}";
    }

    /**
     * Clear recommendation cache
     *
     * @param string|null $type
     * @return void
     */
    public function clearCache(string $type = null): void
    {
        if ($type) {
            Cache::forget($this->getCacheKey($type, '*', '*'));
        } else {
            // Clear all recommendation caches (this is a simplified approach)
            // In production, you might want to use cache tags for better cache management
            Cache::flush();
        }
    }
}
