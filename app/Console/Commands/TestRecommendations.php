<?php

namespace App\Console\Commands;

use App\Models\Cart;
use App\Models\Product;
use App\Services\App\ProductRecommendationService;
use Illuminate\Console\Command;

class TestRecommendations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recommendations:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test product recommendations to verify they work without SQL errors';

    /**
     * Execute the console command.
     */
    public function handle(ProductRecommendationService $recommendationService): int
    {
        $this->info('Testing product recommendations...');
        
        try {
            // Get a cart with items (or create a test scenario)
            $cart = Cart::with('items.product')->where('status', 'active')->first();
            
            if (!$cart) {
                $this->warn('No active cart found. Creating a test cart...');
                
                // Create a test cart with some products
                $cart = Cart::create(['status' => 'active']);
                $products = Product::inStock()->limit(2)->get();
                
                if ($products->count() > 0) {
                    foreach ($products as $product) {
                        $cart->items()->create([
                            'product_id' => $product->id,
                            'quantity' => 1
                        ]);
                    }
                    $this->info("Created test cart with {$products->count()} products.");
                } else {
                    $this->error('No products found in database. Cannot test recommendations.');
                    return Command::FAILURE;
                }
            }
            
            $this->info("Testing with cart ID: {$cart->id}");
            $this->info("Cart has {$cart->items->count()} items");
            
            // Test the recommendation service
            $recommendations = $recommendationService->getCartUpsells($cart, 6);
            
            $this->info("Successfully generated {$recommendations->count()} recommendations:");
            
            foreach ($recommendations as $index => $product) {
                $this->line("  " . ($index + 1) . ". {$product->name} - {$product->price}zł (Category: {$product->category->name})");
            }
            
            $this->info('✅ Recommendations test completed successfully!');
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('❌ Recommendations test failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}
