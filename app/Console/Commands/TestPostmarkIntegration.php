<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\User;
use App\Notifications\OrderStatusChanged;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class TestPostmarkIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'postmark:test {--email=<EMAIL> : Email address to send test to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Postmark email integration with various email types';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $testEmail = $this->option('email');
        
        $this->info('Testing Postmark Integration');
        $this->info('========================');
        $this->newLine();

        // Test 1: Basic mail configuration
        $this->info('1. Testing mail configuration...');
        $this->testMailConfiguration();
        $this->newLine();

        // Test 2: Simple test email
        $this->info('2. Sending simple test email...');
        $this->sendSimpleTestEmail($testEmail);
        $this->newLine();

        // Test 3: Order status notification
        $this->info('3. Testing order status notification...');
        $this->testOrderStatusNotification($testEmail);
        $this->newLine();

        // Test 4: Password reset email (if user exists)
        $this->info('4. Testing password reset email...');
        $this->testPasswordResetEmail($testEmail);
        $this->newLine();

        $this->info('✅ Postmark integration test completed!');
        $this->info('Check your email inbox and the mail logs at storage/logs/mail.log');
    }

    /**
     * Test mail configuration
     */
    protected function testMailConfiguration()
    {
        $mailer = config('mail.default');
        $fromAddress = config('mail.from.address');
        $fromName = config('mail.from.name');
        $postmarkToken = config('mail.mailers.postmark.token');

        $this->line("   Mail Driver: {$mailer}");
        $this->line("   From Address: {$fromAddress}");
        $this->line("   From Name: {$fromName}");
        
        if ($mailer === 'postmark') {
            if ($postmarkToken && $postmarkToken !== 'your-postmark-server-token-here') {
                $this->line("   Postmark Token: " . substr($postmarkToken, 0, 10) . "...");
                $this->info("   ✅ Postmark configuration looks good");
            } else {
                $this->error("   ❌ Postmark token not configured properly");
                $this->warn("   Please set POSTMARK_TOKEN in your .env file");
            }
        } else {
            $this->warn("   ⚠️  Mail driver is not set to 'postmark'");
        }
    }

    /**
     * Send simple test email
     */
    protected function sendSimpleTestEmail($email)
    {
        try {
            Mail::raw('This is a test email from your Laravel application using Postmark.', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Postmark Integration Test - Simple Email');
            });
            
            $this->info("   ✅ Simple test email sent to {$email}");
        } catch (\Exception $e) {
            $this->error("   ❌ Failed to send simple test email: " . $e->getMessage());
        }
    }

    /**
     * Test order status notification
     */
    protected function testOrderStatusNotification($email)
    {
        try {
            // Create a test order or use existing one
            $order = Order::first();
            
            if (!$order) {
                $this->warn("   ⚠️  No orders found in database. Creating mock order data...");
                
                // Create mock order data for testing
                $mockOrder = new \stdClass();
                $mockOrder->id = 12345;
                $mockOrder->uuid = 'test-uuid-' . time();
                $mockOrder->first_name = 'Test';
                $mockOrder->last_name = 'User';
                $mockOrder->email = $email;
                $mockOrder->total = 99.99;
                $mockOrder->created_at = now();
                $mockOrder->inpost_tracking_number = 'INP123456789';
                
                // Create mock user
                $mockUser = new \stdClass();
                $mockUser->first_name = 'Test';
                $mockUser->email = $email;
                
                $mockOrder->user = $mockUser;
                
                // Create mock delivery address
                $mockDeliveryMethod = new \stdClass();
                $mockDeliveryMethod->name = 'InPost Courier';
                
                $mockDeliveryAddress = new \stdClass();
                $mockDeliveryAddress->deliveryMethod = $mockDeliveryMethod;
                
                $mockOrder->deliveryAddress = $mockDeliveryAddress;
                
                $order = $mockOrder;
            }

            // Create anonymous notifiable for testing
            $notifiable = new class($email, 'Test') {
                public $email;
                public $first_name;

                public function __construct($email, $first_name)
                {
                    $this->email = $email;
                    $this->first_name = $first_name;
                }

                public function routeNotificationForMail()
                {
                    return $this->email;
                }
            };

            // Test different order statuses
            $statuses = ['processing', 'shipped', 'delivered'];
            
            foreach ($statuses as $status) {
                $notification = new OrderStatusChanged($order, $status);
                $notifiable->notify($notification);
                $this->line("   ✅ Order status notification sent: {$status}");
            }
            
        } catch (\Exception $e) {
            $this->error("   ❌ Failed to send order status notification: " . $e->getMessage());
        }
    }

    /**
     * Test password reset email
     */
    protected function testPasswordResetEmail($email)
    {
        try {
            $user = User::where('email', $email)->first();
            
            if ($user) {
                // Don't actually send password reset in test
                $this->line("   ✅ User found with email {$email}");
                $this->line("   ℹ️  Password reset email test skipped (would send real reset link)");
            } else {
                $this->warn("   ⚠️  No user found with email {$email}");
                $this->line("   ℹ️  Password reset email test skipped");
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Error checking for user: " . $e->getMessage());
        }
    }
}
