<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ValidateMailConfiguration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:validate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate mail configuration for production readiness';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Mail Configuration Validation');
        $this->info('==============================');
        $this->newLine();

        $isValid = true;

        // Check mail driver
        $mailer = config('mail.default');
        $this->checkConfiguration('Mail Driver', $mailer, $mailer === 'postmark');

        // Check from address
        $fromAddress = config('mail.from.address');
        $this->checkConfiguration(
            'From Address', 
            $fromAddress, 
            $fromAddress && $fromAddress !== '<EMAIL>'
        );

        // Check from name
        $fromName = config('mail.from.name');
        $this->checkConfiguration(
            'From Name', 
            $fromName, 
            $fromName && $fromName !== 'Example'
        );

        if ($mailer === 'postmark') {
            // Check Postmark token
            $token = config('mail.mailers.postmark.token');
            $tokenValid = $token && $token !== 'your-postmark-server-token-here' && strlen($token) > 10;
            $this->checkConfiguration('Postmark Token', $tokenValid ? 'Configured' : 'Not configured', $tokenValid);
            
            if (!$tokenValid) {
                $isValid = false;
            }

            // Check message stream
            $streamId = config('mail.mailers.postmark.message_stream_id');
            $this->checkConfiguration('Message Stream ID', $streamId, !empty($streamId));

            // Check timeout
            $timeout = config('mail.mailers.postmark.timeout');
            $this->checkConfiguration('Timeout', $timeout . 's', $timeout > 0);
        }

        // Check queue configuration
        $queueDriver = config('queue.default');
        $this->checkConfiguration('Queue Driver', $queueDriver, $queueDriver !== 'sync');

        $this->newLine();

        if ($isValid) {
            $this->info('✅ Mail configuration is valid for production!');
        } else {
            $this->error('❌ Mail configuration has issues that need to be resolved.');
            $this->newLine();
            $this->warn('Please check the following:');
            $this->line('1. Set POSTMARK_TOKEN in your .env file');
            $this->line('2. Configure MAIL_FROM_ADDRESS with a verified sender');
            $this->line('3. Set appropriate MAIL_FROM_NAME');
            $this->line('4. Ensure queue workers are configured for production');
        }

        return $isValid ? 0 : 1;
    }

    /**
     * Check and display configuration item
     */
    protected function checkConfiguration($label, $value, $isValid)
    {
        $status = $isValid ? '✅' : '❌';
        $this->line("   {$status} {$label}: {$value}");
    }
}
