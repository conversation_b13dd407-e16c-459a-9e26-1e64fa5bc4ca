@if(isset($upsells) && $upsells->count() > 0)
<div class="col-lg-6 andro_upsells">

    <div class="section-title flex-title">
        <h4 class="title">Polecane produkty</h4>
        <div class="andro_arrows">
        <i class="fa fa-arrow-left slick-arrow slider-prev"></i>
        <i class="fa fa-arrow-right slick-arrow slider-next"></i>
        </div>
    </div>

    <!-- Upsells Start -->
    <div class="andro_upsells-slider">

        @foreach($upsells as $product)
        <!-- Product Start -->
        <div class="andro_product andro_product-list andro_product-has-controls andro_product-has-buttons">
            <div class="andro_product-thumb">
                <a href="{{ $product->url }}">
                    <img src="{{ $product->cart_img }}" alt="{{ $product->name }}">
                </a>
            </div>
            <div class="andro_product-body">
                <!-- <div class="andro_rating-wrapper">
                    <div class="andro_rating">
                        {{-- Static 5-star rating for now - can be made dynamic later --}}
                        <i class="fa fa-star active"></i>
                        <i class="fa fa-star active"></i>
                        <i class="fa fa-star active"></i>
                        <i class="fa fa-star active"></i>
                        <i class="fa fa-star active"></i>
                    </div>
                    <span>5 Stars</span>
                </div> -->
                <h5 class="andro_product-title">
                    <a href="{{ $product->url }}">{{ $product->name }}</a>
                </h5>
                <div class="andro_product-price">
                    @if($product->currentDiscount())
                        <span>{{ number_format($product->discounted_price, 2) }}zł</span>
                        <span>{{ number_format($product->price, 2) }}zł</span>
                    @else
                        <span>{{ number_format($product->price, 2) }}zł</span>
                    @endif
                </div>
                <p>{{ Str::limit(strip_tags($product->short_description ?: $product->description), 100) }}</p>
            </div>
            <div class="andro_product-footer">
                <div class="andro_product-buttons">
                    @if($product->in_cart)
                        <span class="andro_btn-custom success">W koszyku</span>
                    @else
                        <a href="{{ route('cart.add', $product) }}" class="andro_btn-custom primary"
                           data-product-id="{{ $product->id }}"
                           data-product-name="{{ $product->name }}">
                           Dodaj do koszyka
                        </a>
                    @endif
                    <!-- <a href="{{ $product->url }}" class="andro_btn-custom light">Zobacz szczegóły</a> -->
                </div>
            </div>
        </div>
        <!-- Product End -->
        @endforeach

    </div>
    <!-- Upsells End -->

</div>
@endif